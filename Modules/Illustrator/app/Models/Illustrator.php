<?php

namespace Modules\Illustrator\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Illustrator\Database\Factories\IllustratorFactory;

class Illustrator extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    // protected static function newFactory(): IllustratorFactory
    // {
    //     // return IllustratorFactory::new();
    // }
}
