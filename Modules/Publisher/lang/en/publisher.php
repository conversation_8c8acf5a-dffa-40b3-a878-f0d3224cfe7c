<?php
return [
    'publisher'                      => 'Publisher Lists',
    'add_publisher'                  => 'Add Publisher',
    'edit_publisher'                 => 'Edit Publisher',
    'view_publisher'                 => 'View Publisher',
    'delete_publisher'               => 'Delete Publisher',
    'publisher_name'                 => 'Publisher Name',
    'name'                           => 'Name',
    'slug'                           => 'Slug',
    'action'                         => 'Action',
    'created_at'                     => 'Created On',
    'publisher_add_successfully'     => 'Publisher Added Successfully.',
    'publisher_update_successfully'  => 'Publisher Updated Successfully.',
    'publisher_delete_successfully'  => 'Publisher deleted Successfully.',
    'please_select_one_publisher'    => 'Please select at least 1 publisher.',
    'name_placeholder'               => 'Enter Publisher Name',
    'slug_placeholder'               => 'Enter Slug',
    'something_wrong'                => 'Something went wrong.',
    'delete'                         => 'Delete',
    'feature_not_available'          => 'This feature is not available.',

    // Validation messages
    'name_required'                  => 'The publisher name field is required.',
    'slug_required'                  => 'The slug field is required.',
    'slug_unique'                    => 'The slug has already been taken.',
];
