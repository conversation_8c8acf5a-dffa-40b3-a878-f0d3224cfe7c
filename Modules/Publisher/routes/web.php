<?php

use Illuminate\Support\Facades\Route;
use Modules\Publisher\app\Http\Controllers\PublisherController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['prefix' => 'admin', 'as' => 'admin.','middleware' => 'auth:admin'], function () {
    Route::resource('publishers', PublisherController::class)->names('publishers');
    Route::post('publishers/bulk-action', [PublisherController::class, 'bulkAction'])->name('publishers.bulk-action');
});
