<div class="language-switcher mb-3">
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary language-btn active" data-language="en">English</button>
        <button type="button" class="btn btn-outline-primary language-btn" data-language="ar">Arabic</button>
    </div>
    <input type="hidden" name="current_language" id="current_language" value="en">
</div>

@push('styles')
<style>
    .language-switcher .btn-group {
        margin-bottom: 15px;
    }

    .language-switcher .language-btn.active {
        background-color: #0d6efd;
        color: white;
    }

    [dir="rtl"] .translatable-field {
        text-align: right;
        direction: rtl;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const languageBtns = document.querySelectorAll('.language-btn');
        const currentLanguageInput = document.getElementById('current_language');

        // Initially hide Arabic fields but don't disable them
        updateFieldsVisibility('en');

        // Critical form submission handler
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function() {
                // IMPORTANT: Make sure hidden fields are still enabled and will be submitted
                document.querySelectorAll('.translatable-field').forEach(field => {
                    field.disabled = false;
                });

                return true;
            });
        }

        languageBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const language = this.getAttribute('data-language');

                // Update active button
                languageBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Update hidden input
                currentLanguageInput.value = language;

                // Update fields visibility
                updateFieldsVisibility(language);

                // Update text direction
                if (language === 'ar') {
                    document.querySelectorAll('.translatable-field[data-language="ar"]').forEach(field => {
                        field.setAttribute('dir', 'rtl');
                    });
                } else {
                    document.querySelectorAll('.translatable-field[data-language="en"]').forEach(field => {
                        field.removeAttribute('dir');
                    });
                }
            });
        });

        function updateFieldsVisibility(language) {
            document.querySelectorAll('.translatable-field').forEach(field => {
                const fieldLanguage = field.getAttribute('data-language');
                if (fieldLanguage === language) {
                    field.style.display = 'block';
                } else {
                    // CRUCIAL CHANGE: Hide fields but DO NOT disable them
                    // This ensures they'll be submitted with the form
                    field.style.display = 'none';
                }
            });
        }
    });
</script>
@endpush
