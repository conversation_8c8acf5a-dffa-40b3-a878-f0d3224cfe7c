<form action="{{ $action }}" method="POST" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
    @method('PUT')
    @endif

    @include('publisher::partials.language-switcher')
    <div class="card profile-card">
        <div class="row">
            <div class="col-md-6 mb-3 position-relative">
                <label for="name_en" class="form-label">{{ __('publisher::publisher.name') }}</label>
                <div class="form-group translatable-field" data-language="en">
                    <input type="text" id="name_en" name="name[en]" value="{{ old('name.en', isset($model) ? $model->getTranslation('name', 'en', false) : '') }}" class="form-control @error('name.en') is-invalid @enderror" placeholder="{{ __('publisher::publisher.name_placeholder') }}">
                    @error('name.en')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>
                <div class="form-group translatable-field" data-language="ar" style="display: none;">
                    <input type="text" id="name_ar" name="name[ar]" dir="rtl" value="{{ old('name.ar', isset($model) ? $model->getTranslation('name', 'ar', false) : '') }}" class="form-control @error('name.ar') is-invalid @enderror" placeholder="{{ __('publisher::publisher.name_placeholder') }}">
                    @error('name.ar')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <label for="slug" class="form-label">{{ __('publisher::publisher.slug') }}</label>
                <input type="text" name="slug" id="slug" class="form-control @error('slug') is-invalid @enderror"
                    value="{{ old('slug', $model->slug ?? '') }}"
                    placeholder="{{ __('publisher::publisher.slug_placeholder') }}">
                @error('slug')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-md-12">
                <div class="d-flex align-items-center justify-content-end gap-3">
                    <button type="submit" class="btn btn-sm btn-primary">{{ __('admin.common.submit') }}</button>
                    <a href="{{ route('admin.publishers.index') }}" class="btn btn-sm btn-secondary">{{ __('admin.common.cancel') }}</a>
                </div>
            </div>
        </div>
    </div>
</form>

@push('scripts')
@php
$validator = JsValidator::formRequest('Modules\Publisher\app\Http\Requests\PublisherRequest');
$validator->ignore('');
@endphp
{!! $validator !!}
<script type="module">
        // Auto-generate slug from title
        $('#name_en').on('keyup change', function() {
            var name = $(this).val();
            var slug = generateSlug(name);
            $('#slug').val(slug);
        });

        // Function to generate slug
        function generateSlug(text) {
            if(!text) return '';

            // Convert to lowercase
            var slug = text.toLowerCase();

            // Replace spaces with hyphens
            slug = slug.replace(/\s+/g, '-');

            // Remove special characters
            slug = slug.replace(/[^\w\-]+/g, '');

            // Remove duplicate hyphens
            slug = slug.replace(/\-\-+/g, '-');

            // Remove leading and trailing hyphens
            slug = slug.replace(/^-+/, '').replace(/-+$/, '');

            return slug;
        }
</script>
@endpush
