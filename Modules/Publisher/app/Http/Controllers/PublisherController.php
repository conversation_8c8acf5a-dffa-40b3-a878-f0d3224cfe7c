<?php

namespace Modules\Publisher\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\Publisher\app\DataGrids\PublisherDataGrid;
use Modules\Publisher\Models\Publisher;
use Modules\Publisher\app\Http\Requests\PublisherRequest;
use Modules\Publisher\app\Repositories\PublisherRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class PublisherController extends Controller
{
    public const INDEX_ROUTE = "admin.publishers.index";

    /**
     * PublisherController constructor.
     * @param PublisherRepository $publisherRepository
     */
    public function __construct(
        protected PublisherRepository $publisherRepository
    ) {
        $this->middleware('permission:create-publisher', ['only' => ['create', 'store']]);
        $this->middleware('permission:update-publisher', ['only' => ['edit', 'update']]);
        $this->middleware(
            'permission:read-publisher|create-publisher|update-publisher|delete-publisher',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-publisher', ['only' => ['destroy']]);
    }

    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = PublisherDataGrid::getHTML();
            return view('publisher::index', compact('dataGridHtml'));
        } catch (\Exception  $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    public function create(Request $request): View|JsonResponse
    {
        try {
            return view('publisher::create', [
                'method'            => 'POST',
                'action'            => route('admin.publishers.store'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    public function store(PublisherRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->publisherRepository->create($data);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('publisher::publisher.publisher_add_successfully'),
                ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function show(Publisher $publisher): View
    {
        return view('publisher::show', ['model' => $publisher]);
    }

    public function edit(Publisher $publisher): View|RedirectResponse
    {
        try {
            return view('publisher::edit', [
                'is_update' => true,
                'model' => $publisher,
                'method' => 'PUT',
                'action' => route('admin.publishers.update', encrypt($publisher->id)),
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update(PublisherRequest $request, Publisher $publisher): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->publisherRepository->update($data, $publisher->id);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('publisher::publisher.publisher_update_successfully'),
                ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function destroy(Publisher $publisher): JsonResponse
    {
        try {
            if ($publisher) {
                $this->publisherRepository->delete($publisher->id);
            }
            return response()->json([
                'status' => 'success',
                'message' => __('publisher::publisher.publisher_delete_successfully'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $actionType = $request->type;
            $result = $this->publisherRepository->bulkAction($ids, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }
}
