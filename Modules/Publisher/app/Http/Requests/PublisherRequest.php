<?php

namespace Modules\Publisher\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PublisherRequest extends FormRequest
{

    protected const SLUG_REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';
    protected const SLUG_UNIQUE_VALIDATION = 'unique:publishers,slug';
    protected const STRING_REGEX = 'regex:/^[a-zA-Z0-9\s]+$/';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $rules = [
            // Translatable fields
            'name' => ['required', 'array'],
            'name.en' => ['required', self::STRING, $maxSize],
            'name.ar' => ['nullable', self::STRING, $maxSize],
        ];

        $routeName = explode('.', request()->route()->getName());
        $route = array_pop($routeName);
        $slugRules = ['required'];

        switch ($route) {
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION]),
                ]);
                break;
            case 'edit':
            case 'update':
                $publisher = $this->route()->parameter('publisher');
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION . ',' . $publisher->id . ',id']),
                ]);
                break;
            default:
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'name.required' => __('publisher::publisher.name_required'),
            'name.en.required' => __('publisher::publisher.name_required'),
            'name.ar.required' => __('publisher::publisher.name_required'),
            'slug.required' => __('publisher::publisher.slug_required'),
            'slug.unique' => __('publisher::publisher.slug_unique'),
        ];
    }
}
