<?php

namespace Modules\Publisher\app\Repositories;

use Modules\Publisher\Models\Publisher;
use App\Repositories\Admin\Repository;

class PublisherRepository extends Repository
{
   /**
     * PublisherRepository constructor.
     * @param Publisher $model
     */
    public function __construct(Publisher $model)
    {
        $this->model = $model;
    }

    public function bulkAction($ids, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $publisher = $this->model->findOrFail($id);
                    $publisher->delete();
                }
                $message = __('publisher::publisher.publisher_delete_successfully');
                break;

            default:
                $type = 'error';
                $message = __('publisher::publisher.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }
}
