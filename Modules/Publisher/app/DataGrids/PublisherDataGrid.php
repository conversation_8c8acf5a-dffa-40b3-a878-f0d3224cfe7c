<?php

namespace Modules\Publisher\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Publisher\Models\Publisher;

class PublisherDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'publisher';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'ASC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'publisher';

    protected const NAME = 'publisher::publisher.publisher_name';
    protected const SLUG = 'publisher::publisher.slug';
    protected const CREATED_AT = 'publisher::publisher.created_at';
    protected const ACTION = 'publisher::publisher.action';

    public function resource()
    {
        return Publisher::select(
                    'publishers.id',
                    'publishers.slug',
                    'publishers.created_at as publisher_created_at',
                    DB::raw("JSON_EXTRACT(publishers.name, '$.en') as name"),
                    DB::raw("COALESCE(JSON_EXTRACT(publishers.name, '$.en'), JSON_EXTRACT(publishers.name, '$.ar')) as name_with_fallback")
                );
    }

    public function columns(): array
    {
        return [
            'checkbox'  => '<input class="select_all checkbox" type="checkbox" />',
            'name'      => __(self::NAME),
            'slug'      => __(self::SLUG),
            'publisher_created_at'=> __(self::CREATED_AT),
            'action'    => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'name' => 'publishers.name',
            'slug' => 'publishers.slug',
            'publisher_created_at' =>'publishers.created_at',
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'name',
            'slug',
            'publisher_created_at'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'name' => __(self::NAME),
            'slug' => __(self::SLUG),
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'publishers.name' => 'string',
            'publishers.slug' => 'string'
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'delete' => [
                'class' => 'danger',
                'title' => __('publisher::publisher.delete'),
                'module' => 'Publisher',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnName(array $data): string
    {
        $name = $data['name_with_fallback'] ?? $data['name'];
        $name = trim($name, '"');
        return $name;
    }


    public function getSlugColumn(): array
    {
        return [
            'class' => 'text-left',
            'orderable' => true,
            'setCallback' => function ($data) {
                return $data->slug;
            }
        ];
    }

    public function getPublisherCreatedAtColumn(): array
    {
        return [
            'class' => 'text-center',
            'orderable' => true,
            'setCallback' => function ($data) {
                return date('d M Y', strtotime($data->publisher_created_at));
            }
        ];
    }

    public function getColumnAction(array $data): string
    {
        $return = '';

        if (Auth::user()->hasPermission('update-publisher')) {
            $return .= '<a class="me-3" href="' . route('admin.publishers.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if (Auth::user()->hasPermission('delete-publisher')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.publishers.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
