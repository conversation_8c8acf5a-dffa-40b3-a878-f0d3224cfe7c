<?php

namespace Modules\Publisher\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Translatable\HasTranslations;

class Publisher extends Model
{
    use HasFactory, HasTranslations;

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = ['name'];

    /**
     * The default locale to use for translations.
     *
     * @return string
     */
    public function getDefaultLocale(): string
    {
        return 'en';
    }

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'publishers';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'slug',
    ];

    /**
     * Resolve route binding for encrypted IDs.
     *
     * @param mixed $value
     * @param string|null $field
     * @return self
     */
    public function resolveRouteBinding($value, $field = null): self
    {
        return $this->findOrFail(decrypt($value));
    }
}
