<?php
use App\Constants\AppConstants;
return [
    "role_structure" => [
        "super_admin" => [
            "users" => AppConstants::PERMISSION_MAPPING,
            "profile" => "r,u",
            "roles" => AppConstants::PERMISSION_MAPPING,
            "admins" => AppConstants::PERMISSION_MAPPING,
            "categories" => AppConstants::PERMISSION_MAPPING,
            "countries" => AppConstants::PERMISSION_MAPPING,
            "states" => AppConstants::PERMISSION_MAPPING,
            "cities" => AppConstants::PERMISSION_MAPPING,
            "email-templates" => AppConstants::PERMISSION_MAPPING,
            "newsletters" => AppConstants::PERMISSION_MAPPING,
            "pages" => AppConstants::PERMISSION_MAPPING,
            "sections" => AppConstants::PERMISSION_MAPPING,
            "author" => AppConstants::PERMISSION_MAPPING,
            "book" => AppConstants::PERMISSION_MAPPING,
            "global-settings" => "r,u",
            "dynamic-cms" => AppConstants::PERMISSION_MAPPING,
            "banners" => AppConstants::PERMISSION_MAPPING,
            "qr-code" => AppConstants::PERMISSION_MAPPING,
            "faqs" => AppConstants::PERMISSION_MAPPING,
            "faq-category" => AppConstants::PERMISSION_MAPPING,
            "blog" => AppConstants::PERMISSION_MAPPING,
            "blog-category" => AppConstants::PERMISSION_MAPPING,
            "testimonial" => AppConstants::PERMISSION_MAPPING,
            "calendar-event" => AppConstants::PERMISSION_MAPPING,
            "award" => AppConstants::PERMISSION_MAPPING,
            "event-registrations" => AppConstants::PERMISSION_MAPPING,
            "submission" => AppConstants::PERMISSION_MAPPING,
            "critical-acclaim" => AppConstants::PERMISSION_MAPPING,
            "publisher" => AppConstants::PERMISSION_MAPPING,
            "illustrator" => AppConstants::PERMISSION_MAPPING,
            "support-ticket" => "r,u,d,cr,dr,rr,ur",
        ],
        "admin_user" => [
            "profile" => "r,u",
            "roles" => AppConstants::PERMISSION_MAPPING,
            "admins" => AppConstants::PERMISSION_MAPPING,
            "categories" => AppConstants::PERMISSION_MAPPING,
            "countries" => AppConstants::PERMISSION_MAPPING,
            "email-templates" => AppConstants::PERMISSION_MAPPING,
            "newsletters" => AppConstants::PERMISSION_MAPPING,
            "pages" => AppConstants::PERMISSION_MAPPING,
            "sections" => AppConstants::PERMISSION_MAPPING,
            "author" => AppConstants::PERMISSION_MAPPING,
            "book" => AppConstants::PERMISSION_MAPPING,
            "global-settings" => "r,u",
            "blog" => AppConstants::PERMISSION_MAPPING,
            "blog-category" => AppConstants::PERMISSION_MAPPING,
            "testimonial" => AppConstants::PERMISSION_MAPPING,
            "calendar-event" => AppConstants::PERMISSION_MAPPING,
            "award" => AppConstants::PERMISSION_MAPPING,
            "event-registrations" => AppConstants::PERMISSION_MAPPING,
            "submission" => AppConstants::PERMISSION_MAPPING,
            "critical-acclaim" => AppConstants::PERMISSION_MAPPING,
            "publisher" => AppConstants::PERMISSION_MAPPING,
            "illustrator" => AppConstants::PERMISSION_MAPPING,
        ],
        "normal_user" => [
            "profile" => "r,u",
        ],
    ],
    "permission_structure" => [
        "cru_user" => [
            "profile" => "c,r,u",
        ],
    ],
    "permissions_map" => [
        "c" => "create",
        "r" => "read",
        "u" => "update",
        "d" => "delete",
        "cr" => "create-reply",
        "dr" => "delete-reply",
        "rr" => "read-reply",
        "ur" => "update-reply",
    ],
];
