@php
$routeName = Route::currentRouteName();
@endphp
<div class="aside-backdrop"></div>
<aside class="global-sidebar">

        <div class="aside-header">
            <h1 class="logo-wrapper">
            <a href="{{route('admin.dashboard')}}" class="d-inline-block">
                <img src="{{ siteLogo() }}"
                 alt="indianic-logo" width="130" height="30" class="">
            </a>
            </h1>
        </div>
    <div class="aside-body">
        <div class="components">
                <!-- START : URLs Listing Component's Head -->
                <div class="components-head">
                <span>{{ __('admin.left_sidebar.main') }}</span>
            </div>
            <!-- END : URLs Listing Component's Head -->

                <!-- START : URLs Listing -->
                <ul class="list-unstyled accordion main-menu">
                    <li>
                        <a class="sidebar-link {{ strpos($routeName, 'admin.dashboard') === 0 ? 'active' : '' }}"
                        href="{{route('admin.dashboard')}}" aria-label="dashboard">
                        <span class="inic inic-home"></span>
                        <span class="sidebar-link-text text-truncate"
                         data-bs-toggle="tooltip" title="Dashboard">{{ __('admin.left_sidebar.dashboard') }}</span>

                        </a>
                    </li>
                    @if (Auth::user()->hasPermission('read-users') ||
                    Auth::user()->hasPermission('read-admins') ||
                    Auth::user()->hasPermission('read-roles'))
                        <li class="mb-2
                        {{ strpos($routeName, 'admin.users') === 0 ||
                        strpos($routeName, 'admin.admins') === 0 ||
                        strpos($routeName, 'admin.roles') === 0 ? 'active' : '' }}">
                            <div class="accordion-item">
                            <h2 class="accordion-header">
                                <a href="javascript:;" class="sidebar-link accordion-button
                                {{ strpos($routeName, 'admin.users') === 0 ||
                                strpos($routeName, 'admin.admins') === 0 ||
                                strpos($routeName, 'admin.roles') === 0 ? 'active' : 'collapsed' }}"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapseOne"
                                aria-expanded="false" aria-controls="collapseOne">
                                <span class="inic inic-person"></span>
                                <span class="sidebar-link-text text-truncate"
                                data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.users') }}">
                                    {{ __('admin.left_sidebar.users') }}</span>
                                </a>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse
                             {{ strpos($routeName, 'admin.users') === 0 ||
                             strpos($routeName, 'admin.admins') === 0 ||
                             strpos($routeName, 'admin.roles') === 0 ? 'show' : '' }}"
                             data-bs-parent="#accordionExample">
                                <ul class="list-unstyled sub-menu accordion-body">
                                    @permission('read-users')
                                    <li>
                                        <a href="{{ route('admin.users.index') }}" class="sidebar-link
                                         {{ strpos($routeName, 'admin.users') === 0 ? 'active' : '' }}">
                                         <span class="sidebar-link-text text-truncate"
                                         data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.users_list') }}">
                                             {{ __('admin.left_sidebar.users_list') }}
                                            </span>
                                        </a>
                                    </li>
                                    @endpermission
                                    @permission('read-admins')
                                    <li>
                                        <a href="{{ route('admin.admins.index') }}"
                                         class="sidebar-link
                                         {{ strpos($routeName, 'admin.admins') === 0 ? 'active' : '' }}">
                                         <span class="sidebar-link-text text-truncate"
                                         data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.admin_users') }}">
                                             {{ __('admin.left_sidebar.admin_users') }}
                                            </span>
                                        </a>
                                    </li>
                                    @endpermission
                                    @permission('read-roles')
                                    <li>
                                        <a href="{{ route('admin.roles.index') }}"
                                         class="sidebar-link {{ strpos($routeName, 'admin.roles') === 0 ?
                                          'active' : '' }}">
                                          <span class="sidebar-link-text text-truncate"
                                          data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.roles') }}">
                                              {{ __('admin.left_sidebar.roles') }}
                                            </span>
                                        </a>
                                    </li>
                                    @endpermission
                                </ul>
                            </div>
                            </div>
                        </li>
                    @endif

                    @if (Auth::user()->hasPermission('read-categories') ||
                    Auth::user()->hasPermission('read-countries') || Auth::user()->hasPermission('read-cities') ||
                    Auth::user()->hasPermission('read-states'))
                    <li class="{{ strpos($routeName, 'admin.categories') === 0 ||
                     strpos($routeName, 'admin.countries') === 0 || strpos($routeName, 'admin.states') === 0
                     || strpos($routeName, 'admin.cities') === 0
                       ? 'active' : '' }}">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                            <a href="javascript:;" class="sidebar-link accordion-button
                            {{ strpos($routeName, 'admin.categories') === 0 ||
                            strpos($routeName, 'admin.countries') === 0  ||
                            strpos($routeName, 'admin.cities') === 0 ||
                            strpos($routeName, 'admin.states') === 0 ? 'active' : 'collapsed' }}"
                            data-bs-toggle="collapse" data-bs-target="#collapseMaster"
                            aria-expanded="false" aria-controls="collapseMaster"
                             aria-label="master">
                                <span class="inic inic-crown"></span>
                                <span class="sidebar-link-text text-truncate"
                                data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.master') }}">
                                    {{ __('admin.left_sidebar.master') }}
                                </span>
                            </a>
                            </h2>

                            <div id="collapseMaster" class="accordion-collapse collapse
                            {{ strpos($routeName, 'admin.categories') === 0 ||
                            strpos($routeName, 'admin.countries') === 0 || strpos($routeName, 'admin.cities') === 0 ||
                            strpos($routeName, 'admin.states') === 0
                             ? 'show' : '' }}"
                            data-bs-parent="#accordionMaster">
                            <ul class="list-unstyled sub-menu accordion-body">
                                @if (Module::isEnabled('Country') && Auth::user()->hasPermission('read-countries'))
                                <li id='Country' class='Country'>
                                <a href="{{ route('admin.countries.index') }}"
                                class="sidebar-link {{ strpos($routeName, 'admin.countries') === 0
                                  ? 'active' : '' }}" aria-label="country">
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.country') }}">
                                        {{ __('admin.left_sidebar.country') }}
                                    </span>
                                </a>
                                </li>
                                @endif
                                @if (Module::isEnabled('State') && Auth::user()->hasPermission('read-states'))
                                <li id='State' class='State'>
                                <a href="{{ route('admin.states.index') }}"
                                class="sidebar-link {{ strpos($routeName, 'admin.states') === 0
                                ? 'active' : '' }}" aria-label="state">
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.states') }}">
                                        {{ __('admin.left_sidebar.states') }}
                                    </span>
                                </a>
                                </li>
                                @endif
                                @if (Module::isEnabled('City') && Auth::user()->hasPermission('read-cities'))
                                <li id='City' class='City'>
                                <a href="{{ route('admin.cities.index') }}"
                                 class="sidebar-link {{ strpos($routeName, 'admin.cities') === 0
                                 ? 'active' : '' }}" aria-label="city">
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.cities') }}">
                                        {{ __('admin.left_sidebar.cities') }}
                                    </span>
                                </a>
                                </li>
                                @endif
                                @if (Module::isEnabled('Category') && Auth::user()->hasPermission('read-categories'))
                                <li id='Category' class='Category'>
                                    <a href="{{ route('admin.categories.index') }}"
                                     class="sidebar-link {{ strpos($routeName, 'admin.categories') === 0
                                     ? 'active' : '' }}"
                                      aria-label="category">
                                        <span class="sidebar-link-text text-truncate"
                                        data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.categories') }}">
                                            {{ __('admin.left_sidebar.categories') }}
                                        </span>
                                    </a>
                                </li>
                                @endif
                            </ul>
                            </div>
                        </div>
                </li>
                @endif
                @if(config('constants.DEVELOPER_ACCESS'))
                    <li class="{{
                        (strpos($routeName, 'admin.modules') === 0 && ($routeName == 'admin.modules.edit'
                         || $routeName == 'admin.modules.create'
                            || $routeName == 'admin.modules.index') ) ||
                     strpos($routeName, 'admin.permissions') === 0 ||
                     strpos($routeName, 'admin.push_notification') === 0
                       ? 'active' : ''
                       }}">
                        <div class="accordion" id="accordionDeveloper">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                            <a href="javascript:;" class="sidebar-link accordion-button collapsed
                            {{
                                (strpos($routeName, 'admin.modules') === 0 &&
                                ($routeName == 'admin.modules.edit' || $routeName == 'admin.modules.create'
                            || $routeName == 'admin.modules.index') ) ||
                            strpos($routeName, 'admin.permissions') === 0 ||
                            strpos($routeName, 'admin.push_notification') === 0
                            ? 'active' : 'collapsed'
                            }}"
                            data-bs-toggle="collapse" data-bs-target="#collapseDeveloper"
                            aria-expanded="false" aria-controls="collapseDeveloper"
                            aria-label="developer">
                            <span class="inic inic-code"></span>
                            <span class="sidebar-link-text text-truncate"
                            data-bs-toggle="tooltip" title="Master Modules">
                                {{ __('admin.left_sidebar.developer') }}
                            </span>
                            </a>
                            </h2>
                            <div id="collapseDeveloper" class="accordion-collapse collapse
                            {{
                            (strpos($routeName, 'admin.modules') === 0 &&
                            ($routeName == 'admin.modules.edit' || $routeName == 'admin.modules.create'
                            || $routeName == 'admin.modules.index') ) ||
                            strpos($routeName, 'admin.permissions') === 0 ||
                            strpos($routeName, 'admin.push_notification') === 0  ?
                            'show' : '' }}"
                            data-bs-parent="#accordionDeveloper">
                                <ul class="list-unstyled sub-menu accordion-body">
                                    <li>
                                        <a href="{{ route('admin.modules.index') }}"
                                         class="sidebar-link {{ strpos($routeName, 'admin.modules') === 0 &&
                                            ($routeName == 'admin.modules.edit' || $routeName == 'admin.modules.create'
                                             || $routeName == 'admin.modules.index')
                                          ? 'active' : '' }}">
                                          <span class="sidebar-link-text text-truncate"
                                          data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.modules') }}">
                                              {{ __('admin.left_sidebar.modules') }}
                                            </span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('admin.permissions.index') }}"
                                         class="sidebar-link {{ strpos($routeName, 'admin.permissions') === 0 ?
                                         'active' : '' }}">
                                         <span class="sidebar-link-text text-truncate"
                                         data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.permissions') }}">
                                             {{ __('admin.left_sidebar.permissions') }}
                                        </span>
                                        </a>
                                    </li>
                                    @if (Module::isEnabled('PushNotification'))
                                    <li class="menu-list-item" id='PushNotification' class='PushNotification'>
                                        <a href="{{ route('admin.push_notification.create') }}" class="sidebar-link
                                        {{ strpos($routeName, 'admin.push_notification') === 0 ? 'active' : '' }}"
                                        aria-label="pushnotification">
                                            <span class="sidebar-link-text text-truncate"
                                            data-bs-toggle="tooltip"
                                            title="{{ __('admin.left_sidebar.push_notification') }}">
                                                {{ __('admin.left_sidebar.push_notification') }}
                                            </span>
                                        </a>
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>
                @endif
            </ul>
            <!-- END : URLs Listing -->
        </div>
        <div class="components">
            <!-- START : URLs Listing Component's Head -->
            <div class="components-head">
                <span>{{ __('admin.left_sidebar.others') }}</span>
            </div>
            <!-- END : URLs Listing Component's Head -->

            <!-- START : URLs Listing -->
            <ul class="list-unstyled accordion main-menu">
                @if (Module::isEnabled('Page') && Auth::user()->hasPermission('read-pages'))
                <li id='Page' class='Page'>
                    <a href="{{ route('admin.pages.index') }}"
                        class="sidebar-link {{ strpos($routeName, 'admin.pages') === 0 ? 'active' : '' }}"
                        aria-label="pages">
                    <span class="inic inic-file"></span>
                    <span class="sidebar-link-text text-truncate"
                    data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.pages') }}">
                    {{ __('admin.left_sidebar.pages') }}</span>
                    </a>
                </li>
                @endif

                @if (Module::isEnabled('Section') && Auth::user()->hasPermission('read-sections'))
                <li id='Section' class='Section'>
                    <a href="{{ route('admin.sections.index') }}"
                        class="sidebar-link {{ strpos($routeName, 'admin.sections') === 0 ? 'active' : '' }}"
                        aria-label="sections">
                    <span class="inic inic-file"></span>
                    <span class="sidebar-link-text text-truncate"
                    data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.sections') }}">
                    {{ __('admin.left_sidebar.sections') }}</span>
                    </a>
                </li>
                @endif

                <li class="{{ strpos($routeName, 'admin.blog-category') === 0 ||
                strpos($routeName, 'admin.blog') === 0 ||
                strpos($routeName, 'admin.faqs') === 0 ||
                strpos($routeName, 'admin.faq-category') === 0 ? 'active' : '' }}">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <a href="javascript:;" class="sidebar-link accordion-button
                             {{ strpos($routeName, 'admin.blog-category') === 0 ||
                                strpos($routeName, 'admin.blog') === 0 ||
                                strpos($routeName, 'admin.faqs') === 0 ||
                                strpos($routeName, 'admin.faq-category') === 0 ? 'active' : 'collapsed' }}"
                              data-bs-toggle="collapse" data-bs-target="#collapseContent" aria-expanded="false"
                               aria-controls="collapseContent" aria-label="content">
                            <span class="inic inic-file"></span>
                            <span class="sidebar-link-text text-truncate"
                            data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.content') }}">
                            {{ __('admin.left_sidebar.content') }}</span>
                            </a>
                        </h2>
                        <div id="collapseContent"
                            class="accordion-collapse collapse
                            {{ strpos($routeName, 'admin.blog') === 0 ||
                            strpos($routeName, 'admin.blog-category') === 0 ||
                            strpos($routeName, 'admin.faqs') === 0 ||
                            strpos($routeName, 'admin.faq-category') === 0 ? 'show' : '' }}"
                            data-bs-parent="#accordionContent">
                            <ul class="list-unstyled sub-menu accordion-body">
                               {{--  <li>
                                    <a href="static-pages.html" class="sidebar-link" aria-label="static-page">
                                        <span class="sidebar-link-text text-truncate">{{
                                            __('admin.left_sidebar.static_pages') }}</span>
                                    </a>
                                </li> --}}
                                <li>
                                    <div class="accordion" id="accordionBlogs">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <a href="javascript:;" class="sidebar-link accordion-button collapsed  '
                                                {{
                                                strpos($routeName, 'admin.blog') === 0 ||
                                                strpos($routeName, 'admin.blog-category') === 0 ? 'active' : 'collapsed'
                                                }}"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseBlogs"
                                                    aria-expanded="false" aria-controls="collapseBlogs"
                                                    aria-label="blogs">
                                                    <span class="sidebar-link-text text-truncate"
                                                    data-bs-toggle="tooltip" title="Blog">{{
                                                        __('admin.left_sidebar.blogs') }}</span>
                                                </a>
                                            </h2>
                                            @if (Auth::user()->hasPermission('read-blog') ||
                                            Auth::user()->hasPermission('read-blog-category'))
                                                <div id="collapseBlogs" class="accordion-collapse collapse
                                                {{ strpos($routeName, 'admin.blog') === 0 ||
                                                strpos($routeName, 'admin.blog-category') === 0  ? 'show' : '' }}"
                                                    data-bs-parent="#accordionBlogs">
                                                    <ul class="list-unstyled sub-menu accordion-body">
                                                        @if (Auth::user()->hasPermission('read-blog')
                                                        && Module::isEnabled('blog'))
                                                            <li>
                                                                <a href="{{ route('admin.blog.index') }}"
                                                                class="sidebar-link
                                                                {{
                                                                strpos($routeName, 'admin.blog') === 0  ? 'active' : ''
                                                                }}"
                                                                    aria-label="blog-listing">
                                                                    <span class="sidebar-link-text text-truncate"
                                                                    data-bs-toggle="tooltip" title="Blog List">{{
                                                                        __('admin.left_sidebar.blog_list') }}</span></a>
                                                            </li>
                                                        @endif
                                                        @if (Auth::user()->hasPermission('read-blog-category')
                                                        && Module::isEnabled('BlogCategory'))
                                                            <li>
                                                            <a href="{{ route('admin.blog-category.index') }}"
                                                             class="sidebar-link
                                                            {{strpos($routeName, 'admin.blog-category') === 0  ?
                                                             'active' : ''
                                                            }}"
                                                            aria-label="blog-category">
                                                                <span class="sidebar-link-text text-truncate"
                                                                data-bs-toggle="tooltip" title="Blog Category">{{
                                                                    __('admin.left_sidebar.blog_category') }}
                                                                </span>
                                                            </a>
                                                            </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </li>
                                <li
                                    class="{{ strpos($routeName, 'admin.faqs') === 0 ||
                                        strpos($routeName, 'admin.faq-category') === 0  ? 'active' : '' }}">
                                    <div class="accordion" id="accordionFaq">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <a href="javascript:;"
                                                    class="sidebar-link accordion-button
                                                    {{ strpos($routeName, 'admin.faqs') === 0 ||
                                                    strpos($routeName, 'admin.faq-category') === 0  ?
                                                     'active' : 'collapsed' }}"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseFaq"
                                                     aria-expanded="false"
                                                    aria-controls="collapseFaq" aria-label="faq">
                                                    <span class="sidebar-link-text text-truncate"
                                                    data-bs-toggle="tooltip" title="FAQ">
                                                    {{ __('admin.left_sidebar.faqs') }}</span>
                                                </a>
                                            </h2>
                                            <div id="collapseFaq"
                                                class="accordion-collapse collapse
                                                {{ strpos($routeName, 'admin.faqs') === 0 ||
                                                strpos($routeName, 'admin.faq-category') === 0  ? 'show' : '' }}"
                                                data-bs-parent="#accordionFaq">
                                                <ul class="list-unstyled sub-menu accordion-body">
                                                    @if(Module::isEnabled('FAQ') &&
                                                     (Auth::user()->hasPermission('read-faqs') ||
                                                    Auth::user()->hasRole('super_admin')))
                                                    <li id='Faq' class='Faq'>
                                                        <a href="{{ route('admin.faqs.index') }}"
                                                            class="sidebar-link
                                                            {{ strpos($routeName, 'admin.faq-list') === 0
                                                                ? 'active' : '' }}"
                                                            aria-label="faq">
                                                            <span class="sidebar-link-text text-truncate"
                                                            data-bs-toggle="tooltip" title="FAQ List">{{
                                                                __('admin.left_sidebar.faq_list') }}</span>
                                                        </a>
                                                    </li>
                                                    @endif
                                                    @if (Module::isEnabled('FAQCategory') &&
                                                    (Auth::user()->hasPermission('read-faq-category')
                                                    || Auth::user()->hasRole('super_admin')))
                                                    <li id='FAQCategory' class='FAQCategory'>
                                                        <a href="{{ route('admin.faq-category.index') }}"
                                                            class="sidebar-link
                                                            {{ strpos($routeName, 'admin.faq-category') === 0 ?
                                                                 'active' : '' }}"
                                                            aria-label="faq">
                                                            <span class="sidebar-link-text text-truncate"
                                                            data-bs-toggle="tooltip" title="FAQ Category">{{
                                                                __('admin.left_sidebar.faq_category') }}</span>
                                                        </a>
                                                    </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
                @if ((Module::isEnabled('Author') && Auth::user()->hasPermission('read-author')) ||
                     (Module::isEnabled('Book') && Auth::user()->hasPermission('read-book')) ||
                     (Module::isEnabled('Award') && Auth::user()->hasPermission('read-award')) ||
                     (Module::isEnabled('Publisher') && Auth::user()->hasPermission('read-publisher')))
                <li class="{{ strpos($routeName, 'admin.authors') === 0 ||
                            strpos($routeName, 'admin.books') === 0 ||
                            strpos($routeName, 'admin.awards') === 0 ||
                            strpos($routeName, 'admin.publishers') === 0 ? 'active' : '' }}">
                    <div class="accordion" id="accordionPublications">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <a href="javascript:;" class="sidebar-link accordion-button collapsed
                                {{
                                strpos($routeName, 'admin.authors') === 0 ||
                                strpos($routeName, 'admin.books') === 0 ||
                                strpos($routeName, 'admin.awards') === 0 ||
                                strpos($routeName, 'admin.publishers') === 0 ? 'active' : 'collapsed'
                                }}"
                                    data-bs-toggle="collapse" data-bs-target="#collapsePublications"
                                    aria-expanded="false" aria-controls="collapsePublications"
                                    aria-label="publications">
                                    <span class="inic inic-file"></span>
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="Publications">Publications</span>
                                </a>
                            </h2>
                            <div id="collapsePublications" class="accordion-collapse collapse
                            {{ strpos($routeName, 'admin.authors') === 0 ||
                            strpos($routeName, 'admin.books') === 0 ||
                            strpos($routeName, 'admin.awards') === 0 ||
                            strpos($routeName, 'admin.publishers') === 0 ? 'show' : '' }}"
                                data-bs-parent="#accordionPublications">
                                <ul class="list-unstyled sub-menu accordion-body">
                                    @if (Module::isEnabled('Author') && Auth::user()->hasPermission('read-author'))
                                        <li>
                                            <a href="{{ route('admin.authors.index') }}"
                                            class="sidebar-link
                                            {{
                                            strpos($routeName, 'admin.authors') === 0 ? 'active' : ''
                                            }}"
                                                aria-label="author">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Authors">{{ __('admin.left_sidebar.authors') }}</span></a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('Book') && Auth::user()->hasPermission('read-book'))
                                        <li>
                                            <a href="{{ route('admin.books.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.books') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="book">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Books">{{ __('admin.left_sidebar.books') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('Award') && Auth::user()->hasPermission('read-award'))
                                        <li>
                                            <a href="{{ route('admin.awards.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.awards') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="award">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Awards">{{ __('admin.left_sidebar.awards') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('Publisher') && Auth::user()->hasPermission('read-publisher'))
                                        <li>
                                            <a href="{{ route('admin.publishers.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.publishers') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="publisher">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Publishers">{{ __('admin.left_sidebar.publishers') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>
                @endif

                @if ((Module::isEnabled('Testimonial') && Auth::user()->hasPermission('read-testimonial')) ||
                     (Module::isEnabled('Submission') && Auth::user()->hasPermission('read-submission')) ||
                     (Module::isEnabled('CriticalAcclaim') && Auth::user()->hasPermission('read-critical-acclaim')))
                <li class="{{ strpos($routeName, 'admin.testimonials') === 0 ||
                            strpos($routeName, 'admin.submissions') === 0 ||
                            strpos($routeName, 'admin.critical-acclaims') === 0 ? 'active' : '' }}">
                    <div class="accordion" id="accordionReviews">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <a href="javascript:;" class="sidebar-link accordion-button collapsed
                                {{
                                strpos($routeName, 'admin.testimonials') === 0 ||
                                strpos($routeName, 'admin.submissions') === 0 ||
                                strpos($routeName, 'admin.critical-acclaims') === 0 ? 'active' : 'collapsed'
                                }}"
                                    data-bs-toggle="collapse" data-bs-target="#collapseReviews"
                                    aria-expanded="false" aria-controls="collapseReviews"
                                    aria-label="reviews">
                                    <span class="inic inic-verify"></span>
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="Reviews">Reviews</span>
                                </a>
                            </h2>
                            <div id="collapseReviews" class="accordion-collapse collapse
                            {{ strpos($routeName, 'admin.testimonials') === 0 ||
                            strpos($routeName, 'admin.submissions') === 0 ||
                            strpos($routeName, 'admin.critical-acclaims') === 0 ? 'show' : '' }}"
                                data-bs-parent="#accordionReviews">
                                <ul class="list-unstyled sub-menu accordion-body">
                                    @if (Module::isEnabled('Testimonial') && Auth::user()->hasPermission('read-testimonial'))
                                        <li>
                                            <a href="{{ route('admin.testimonials.index') }}"
                                            class="sidebar-link
                                            {{
                                            strpos($routeName, 'admin.testimonials') === 0 ? 'active' : ''
                                            }}"
                                                aria-label="testimonial">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Testimonials">{{ __('admin.left_sidebar.testimonials') }}</span></a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('Submission') && Auth::user()->hasPermission('read-submission'))
                                        <li>
                                            <a href="{{ route('admin.submissions.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.submissions') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="submission">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Submissions">{{ __('admin.left_sidebar.submissions') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('CriticalAcclaim') && Auth::user()->hasPermission('read-critical-acclaim'))
                                        <li>
                                            <a href="{{ route('admin.critical-acclaims.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.critical-acclaims') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="critical-acclaim">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Critical Acclaims">{{ __('admin.left_sidebar.critical_acclaims') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>
                @endif

                @if ((Module::isEnabled('CalendarEvent') && Auth::user()->hasPermission('read-calendar-event')) ||
                     (Module::isEnabled('EventRegistration') && Auth::user()->hasPermission('read-event-registrations')))
                <li class="{{ strpos($routeName, 'admin.calendar-events') === 0 ||
                            strpos($routeName, 'admin.event-registrations') === 0 ? 'active' : '' }}">
                    <div class="accordion" id="accordionEvents">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <a href="javascript:;" class="sidebar-link accordion-button collapsed
                                {{
                                strpos($routeName, 'admin.calendar-events') === 0 ||
                                strpos($routeName, 'admin.event-registrations') === 0 ? 'active' : 'collapsed'
                                }}"
                                    data-bs-toggle="collapse" data-bs-target="#collapseEvents"
                                    aria-expanded="false" aria-controls="collapseEvents"
                                    aria-label="events">
                                    <span class="inic inic-calendar"></span>
                                    <span class="sidebar-link-text text-truncate"
                                    data-bs-toggle="tooltip" title="Events">Events</span>
                                </a>
                            </h2>
                            <div id="collapseEvents" class="accordion-collapse collapse
                            {{ strpos($routeName, 'admin.calendar-events') === 0 ||
                            strpos($routeName, 'admin.event-registrations') === 0 ? 'show' : '' }}"
                                data-bs-parent="#accordionEvents">
                                <ul class="list-unstyled sub-menu accordion-body">
                                    @if (Module::isEnabled('CalendarEvent') && Auth::user()->hasPermission('read-calendar-event'))
                                        <li>
                                            <a href="{{ route('admin.calendar-events.index') }}"
                                            class="sidebar-link
                                            {{
                                            strpos($routeName, 'admin.calendar-events') === 0 ? 'active' : ''
                                            }}"
                                                aria-label="calendar-event">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Calendar Events">{{ __('admin.left_sidebar.calendar_events') }}</span></a>
                                        </li>
                                    @endif
                                    @if (Module::isEnabled('EventRegistration') && Auth::user()->hasPermission('read-event-registrations'))
                                        <li>
                                            <a href="{{ route('admin.event-registrations.index') }}"
                                             class="sidebar-link
                                            {{strpos($routeName, 'admin.event-registrations') === 0 ?
                                             'active' : ''
                                            }}"
                                            aria-label="event-registration">
                                                <span class="sidebar-link-text text-truncate"
                                                data-bs-toggle="tooltip" title="Event Registrations">{{ __('admin.left_sidebar.event_registrations') }}</span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                                </li>
                @endif

                @if (Module::isEnabled('SupportTicket') && Auth::user()->hasPermission('read-support-ticket'))
                <li class="SupportTicket" id="SupportTicket" >
                    <a href="{{ route('admin.support-tickets.index') }}" class="sidebar-link" aria-label="support">
                        <span class="inic inic-support"></span>
                        <span class="sidebar-link-text text-truncate"
                        data-bs-toggle="tooltip" title="Supports">{{ __('admin.left_sidebar.support') }}</span>
                    </a>
                </li>
                @endif
            </ul>
            <!-- END : URLs Listing -->
        </div>
        <div class="components">
            <!-- START : URLs Listing Component's Head -->
            <div class="components-head">
                <span>{{ __('admin.left_sidebar.general') }}</span>
            </div>
            <!-- END : URLs Listing Component's Head -->

                <!-- START : URLs Listing -->
                <ul class="list-unstyled accordion main-menu">
                    @if (Module::isEnabled('EmailTemplate') && Auth::user()->hasPermission('read-email-templates'))
                    <li id='EmailTemplate'  class='EmailTemplate'>
                        <a href="{{ route('admin.email-templates.index') }}"
                         class="sidebar-link {{ strpos($routeName, 'admin.email-templates') === 0 ? 'active' : '' }}"
                          aria-label="email-templates">
                        <span class="inic inic-email"></span>
                        <span class="sidebar-link-text text-truncate"
                        data-bs-toggle="tooltip" title="
                        {{ __('admin.left_sidebar.email_templates') }}">
                            {{ __('admin.left_sidebar.email_templates') }}
                        </span>
                        </a>
                    </li>
                    @endif

                    @if (Module::isEnabled('Newsletter') && Auth::user()->hasPermission('read-newsletters'))
                        <li id='Newsletter' class='Newsletter'>
                            <a href="{{ route('admin.newsletters.index') }}"
                            class="sidebar-link {{ strpos($routeName, 'admin.newsletters') === 0 ? 'active' : '' }}"
                            aria-label="newsletters">
                            <span class="inic inic-send-1"></span>
                            <span class="sidebar-link-text text-truncate"
                            data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.newsletter') }}">
                            {{ __('admin.left_sidebar.newsletter') }}</span>
                        </a>
                    </li>
                    @endif

                    @if (Module::isEnabled('Banner') && Auth::user()->hasPermission('read-banners'))
                    <li id='Banner' class='Banner'>
                        <a href="{{ route('admin.banners.index') }}"
                         class="sidebar-link {{ strpos($routeName, 'admin.banners') === 0 ? 'active' : '' }}"
                          aria-label="dynamiccms">
                        <span class="inic inic-photo"></span>
                        <span class="sidebar-link-text text-truncate"
                        data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.banner') }}">
                        {{ __('admin.left_sidebar.banner') }}</span>
                    </a>
                </li>
                @endif

                    @if (Module::isEnabled('QrCode') && Auth::user()->hasPermission('read-qr-code'))
                    <li id='QrCode' class='QrCode'>
                        <a href="{{ route('admin.qrcodes.index') }}"
                         class="sidebar-link {{ strpos($routeName, 'admin.qrcodes') === 0 ? 'active' : '' }}"
                          aria-label="qrcodes">
                        <span class="inic inic-qr-code"></span>
                        <span class="sidebar-link-text text-truncate"
                        data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.qrcode') }}">
                        {{ __('admin.left_sidebar.qrcode') }}</span>
                        </a>
                    </li>
                    @endif

                    @if (Module::isEnabled('DynamicImport') && Auth::user()->hasPermission('read-dynamic-import'))
                    <li>
                        <a href="{{ route('admin.dynamic-import.index') }}" class="sidebar-link
                         {{ strpos($routeName, 'admin.dynamic-import') === 0 ? 'active' : '' }}"
                          aria-label="DynamicImport">
                        <span class="inic inic-import"></span>
                        <span class="sidebar-link-text text-truncate"
                        data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.dynamic_import') }}">
                            {{ __('admin.left_sidebar.dynamic_import') }}
                        </span>
                        </a>
                    </li>
                    @endif

                    @if (Auth::user()->hasPermission('read-global-settings'))
                    <li class="{{ strpos($routeName, 'admin.getsetting') === 0 ||
                       strpos($routeName, 'admin.getsmtp') === 0  ||
                       strpos($routeName, 'admin.getsocial') === 0 ||
                       strpos($routeName, 'admin.getgs') === 0 ||
                       strpos($routeName, 'admin.getsitemap') === 0 ||
                       strpos($routeName, 'admin.modules_list') === 0  ? 'active' : '' }}">
                        <div class="accordion-item">
                        <h2 class="accordion-header">
                            <a href="javascript:;" class="sidebar-link accordion-button
                              {{ strpos($routeName, 'admin.getsetting') === 0 ||
                                strpos($routeName, 'admin.getsmtp') === 0  ||
                                strpos($routeName, 'admin.getsocial') === 0 ||
                                strpos($routeName, 'admin.getgs') === 0 ||
                                strpos($routeName, 'admin.getsitemap') === 0 ||
                                strpos($routeName, 'admin.modules_list') === 0  ? 'active' : 'collapsed' }}"
                                    data-bs-toggle="collapse" data-bs-target="#collapseSettings" aria-expanded="false"
                                     aria-controls="collapseSettings" aria-label="settings">
                            <span class="inic inic-settings"></span>
                            <span class="sidebar-link-text text-truncate"
                            data-bs-toggle="tooltip" title="{{ __('admin.left_sidebar.settings') }}">
                            {{ __('admin.left_sidebar.settings') }}</span>
                            </a>
                        </h2>
                        <div id="collapseSettings" class="accordion-collapse collapse
                        {{ strpos($routeName, 'admin.getsetting') === 0 ||
                        strpos($routeName, 'admin.getsmtp') === 0  ||
                        strpos($routeName, 'admin.getsocial') === 0 ||
                        strpos($routeName, 'admin.getgs') === 0 ||
                         strpos($routeName, 'admin.getsitemap') === 0  ||
                         strpos($routeName, 'admin.modules_list') === 0  ? 'show' : '' }}"
                          data-bs-parent="#accordionSettings">
                            <ul class="list-unstyled sub-menu accordion-body">
                                <li>
                                    <a href="{{ route('admin.getsetting') }}" class="sidebar-link
                                      {{ strpos($routeName, 'admin.getsetting') === 0  ? 'active' : '' }}"
                                       aria-label="general-settings">
                                       <span class="sidebar-link-text text-truncate"
                                       data-bs-toggle="tooltip" title="Company Info">
                                           {{ __('admin.left_sidebar.company') }}
                                        </span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('admin.getsmtp') }}" class="sidebar-link
                                     {{ strpos($routeName, 'admin.getsmtp') === 0  ? 'active' : '' }}"
                                      aria-label="social-media">
                                      <span class="sidebar-link-text text-truncate"
                                      data-bs-toggle="tooltip" title="SMTP Info">
                                          {{ __('admin.left_sidebar.smtp') }}
                                        </span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('admin.getsocial') }}" class="sidebar-link
                                     {{ strpos($routeName, 'admin.getsocial') === 0  ? 'active' : '' }}"
                                     aria-label="payment-gateway">
                                     <span class="sidebar-link-text text-truncate"
                                     data-bs-toggle="tooltip" title="Social Media">
                                         {{ __('admin.left_sidebar.social_media') }}
                                        </span>
                                    </a>
                                </li>

                                <li>
                                    <a href="{{ route('admin.getgs') }}" class="sidebar-link
                                     {{ strpos($routeName, 'admin.getgs') === 0  ? 'active' : '' }}"
                                      aria-label="email-notifications-setting">
                                        <span class="sidebar-link-text text-truncate"
                                        data-bs-toggle="tooltip" title="General Setting">
                                          {{ __('admin.left_sidebar.general_settings') }}
                                        </span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('admin.getsitemap') }}"
                                     class="sidebar-link
                                     {{ strpos($routeName, 'admin.getsitemap') === 0  ? 'active' : '' }}"
                                      aria-label="email-notifications-setting">
                                      <span class="sidebar-link-text text-truncate"
                                      data-bs-toggle="tooltip" title="Site Map">
                                          {{ __('admin.left_sidebar.sitemap') }}
                                        </span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('admin.modules_list') }}"
                                     class="sidebar-link" aria-label="email-notifications-setting">
                                     <span class="sidebar-link-text text-truncate"
                                     data-bs-toggle="tooltip" title="Module">
                                         {{ __('admin.left_sidebar.modules') }}
                                        </span>
                                    </a>
                                </li>

                            </ul>
                        </div>
                    </div>
                </li>
                @endif
            </ul>
            <!-- END : URLs Listing -->
        </div>
    </div>
    <div class="aside-footer">
        <div class="theme-toggle-switch">
            <input type="checkbox" class="switch-theme" id="theme-toggle">
            <label for="theme-toggle" class="switch-theme-label">
                <span class="inic inic-sun"></span>
                <span class="inic inic-moon"></span>
            </label>
        </div>
        <div class="logged-user-card">
            <div class="logged-user-card-info">
                <figure class="avatar-40">
                    <img class="rounded-circle"
                    src="{{ getImage(auth()->guard('admin')->user()->photo,config('constants.folders.admin'),true) }}"
                    alt="{{ auth()->guard('admin')->user()->name }} {{ __('admin.footer.profile_picture') }}"
                    width="40" height="40">
                </figure>
                <div class="flex-top flex-column">
                    <p class="logged-user-card-name">{{ auth()->guard('admin')->user()->name }}</p>
                    <span class="logged-user-card-mail">{{ auth()->guard('admin')->user()->email }}</span>
                </div>
            </div>

                <div class="dropdown">
                <button class="btn rounded-circle dropdown-toggle btn-only-icon"
                 aria-label="Profile Action" type="button"
                  data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="inic inic-more-1" data-bs-toggle="tooltip"
                    title="{{ __('admin.footer.update_profile') }}"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item"
                        href="{{ route('admin.profile.index') }}">
                        {{ __('admin.left_sidebar.profile') }}
                    </a>
                </li>
                    <li><a class="dropdown-item"
                        href="{{ route('admin.profile.change-password') }}">
                        {{ __('admin.left_sidebar.update_password') }}
                    </a>
                </li>
                </ul>
            </div>
        </div>
    </div>
</aside>
