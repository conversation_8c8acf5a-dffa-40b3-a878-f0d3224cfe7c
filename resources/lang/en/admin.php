<?php
define("MOBILE_NUMBER", "Mobile Number");
define("DYNAMIC_IMPORT", "Dynamic Import");
define("FIRST_NAME", "First Name");
define("LAST_NAME", "Last Name");
define("DISPLAY_NAME", "Display Name");
define("COMFRIM_PASSWORD", "Confirm Password");

return [
    'left_sidebar' => [
        'main' => 'Main',
        'dashboard' => 'Dashboard',
        'users' => 'Users',
        'users_list' => 'Users',
        'admin_users' => 'Admins',
        'roles' => 'Roles',
        'modules' => 'Modules',
        'permissions' => 'Permissions',
        'master' => 'Master',
        'content' => 'Content',
        'static_pages' => 'Static Pages',
        'country' => 'Countries',
        'categories' => 'Categories',
        'general' => 'General',
        'others' => 'Others',
        'email_templates' => 'Email Templates',
        'newsletter' => 'Newsletter',
        'pages' => 'Pages',
        'sections' => 'Sections',
        'banner' => 'Banners',
        'qrcode' => 'QR Code',
        'dynamic_import' => DYNAMIC_IMPORT,
        'push_notification' => 'Test Push Notification',
        'settings' => 'Settings',
        'company' => 'Company',
        'smtp' => 'SMTP',
        'social_media' => 'Social Media',
        'general_settings' => 'General Settings',
        'sitemap' => 'Sitemap',
        'blogs' => 'Blogs',
        'blog_list' => 'Blog List',
        'blog_category' => 'Blog Category',
        'faqs' => 'FAQs',
        'faq_list' => 'FAQ List',
        'faq_category' => 'FAQ Category',
        'support' => 'Support',
        'profile' => 'Profile',
        'update_password' => 'Update Password',
        'cities' => 'Cities',
        'states' => 'States',
        'developer' => 'Developer',
        'authors' => 'Authors',
        'books' => 'Books',
        'testimonials' => 'Testimonials',
        'calendar_events' => 'Calendar Events',
        'awards' => 'Awards',
        'publishers' => 'Publishers',
        'event_registrations' => 'Event Registrations',
        'submissions' => 'Submissions',
        'critical_acclaims' => 'Critical Acclaims',
    ],
    'header' => [
        'notification' => 'Notification',
        'clear_all' => 'Clear all',
    ],
    'common' => [
        'submit'=>'Submit',
        'home' => 'Home',
        'view' => 'View',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'action' => 'Action',
        'status' => 'Status',
        'role' => 'Role',
        'image' => 'Image',
        'date' => 'Date',
        'title' => 'Title',
        'type' => 'Type',
        'active' => 'Active',
        'in_active' => 'Inactive',
        'remove' => 'Remove',
        'close' => 'Close',
        'update' => 'Update',
        'cancel' => 'Cancel',
        'category' => 'Category',
        'sub_category' => 'Subcategory',
        'dashboard' => 'Dashboard',
        'something_wrong' => 'Something went wrong.',
        'are_you_sure' => 'Are you sure?',
        'not_revert' => 'You will not be able to revert this!',
        'yes_delete' => 'Yes, delete !',
        'delivered' => 'Delivered',
        'send_date' => 'Send Date',
        'send_to' => 'Send To',
        'body' => 'Body',
        'send_later' => 'Send Later',
        'send_now' => 'Send Now',
        'all_users' => 'All Users',
        'newsletters' => 'Newsletters',
        'newsletter' => 'Newsletter',
        'date_of_birth' => 'Date of Birth',
        'dob' => 'DOB',
        'display_name' => DISPLAY_NAME,
        'description' => 'Description',
        'modules' => 'Modules',
        'permissions' => 'Permissions',
        'roles' => 'Roles',
        'back' => 'Back',
        'advance_search' => 'Advance Search',
        'select_what_to_download' => 'Select what to download',
        'select_file_type' => 'Select file type',
        'saved_download_selections' => 'Saved download selections',
        'save_template' => 'Save Template',
        'template_name' => 'Template Name',
        'filters' => 'Filters',
        'record_per_page' => 'Record per page',
        'save' => 'Save',
        'apply' => 'Apply',
        'reset' => 'Reset',
        'csv' => 'CSV',
        'excel' => 'Excel',
        'pdf' => 'PDF',
        'save_and_search' => 'Save & Search',
        'saved_filters' => 'Saved Filters',
        'all_duplicates' => 'All Duplicates',
        'all_spam_fake' => 'All Spam-Fake',
        'all_vendors' => 'All Vendors',
        'your_templates' => 'Your Templates:',
        'search' => 'Search',
        'clear' => 'Clear',
        'add_group' => 'Add Group',
        'refresh' => 'Refresh',
        'screen_options' => 'Screen Options',
        'download_options' => 'Download Options',
        'import_file' => 'Import File',
        'name' => 'Name',
        'logged_in' => 'You are logged in!',
        'upload' => 'Upload',
        'translatable_fields' => 'Translatable Fields',
        'general_settings' => 'General Settings',
    ],
    'login_page' => [
        'welcome_back_login_again' => 'Welcome back! Log in to your account.',
        'login' => 'Login',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'remember_password' => 'Remember Password',
        'forgot_password' => 'Forgot Password?',
        'sign_in' => 'Sign In'
    ],
    'password_reset' => [
        'forgot_your_password' => 'Forgot Your Password !',
        'we_got_you_back' => "Don't worry, We got you back.",
        'email' => 'Email',
        'reset_password' => 'Reset Password',
        'login' => 'Login',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'confirm_password' => COMFRIM_PASSWORD,
    ],
    'profile' => [
        'home' => 'Home',
        'profile' => 'Profile',
        'user_profile' => 'User Profile',
        'edit_profile' => 'Edit Profile',
        'update_profile' => 'Update Profile',
        'first_name' => FIRST_NAME,
        'last_name' => LAST_NAME,
        'name' => 'Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'status' => 'Status',
        'mobile_number' => MOBILE_NUMBER,
        'change_password' => 'Change Password',
        'old_password' => 'Old Password',
        'confirm_password' => COMFRIM_PASSWORD,
        'new_password' => 'New Password',
        'profile_edit_success' => 'Profile has been updated successfully.',
        'old_password_not_match' => 'Old password does not match',
        'choose_different_password' => 'New Password cannot be same as your current password.
         Please choose a different password.',
        'fmobile_may_not_contain_whitespace' => 'The mobile may not contain whitespace.',
        'fmobile_required' => 'The mobile field is required.',
        'fmobile_must_be_unique' => 'Mobile no is already taken.',
        'fmobile_not_valid' => 'Please enter valid mobile.',
        'first_name_required' => 'First name is required.',
        'first_name_only_alphabets' => 'First name may only contain letters.',
        'last_name_required' => 'Last name is required.',
        'last_name_only_alphabets' => 'Last name may only contain letters.',
    ],
    'users' => [
        'first_name' => FIRST_NAME,
        'last_name' => LAST_NAME,
        'email' => 'Email',
        'status' => 'Status',
        'password' => 'Password',
        'confirm_password' => COMFRIM_PASSWORD,
        'mobile_number' => MOBILE_NUMBER,
        'mobile_no' => 'Mobile No.',
        'date_of_birth' => 'Date of Birth',
        'user_details' => 'User Details',
        'users' => 'Users',
        'user' => 'User',
        'add_success' => 'User has been added successfully.',
        'edit_success' => 'User has been updated successfully.',
        'delete_success' => 'User has been deleted successfully.',
        'status_change' => 'User has been :status successfully.',
        'add_user' => 'Add User',
        'edit_user' => 'Edit User',
        'user_deleted' => 'User Deleted!',
        'back_to_listing' => 'Back to the users list',
        'status_edit_success' => 'Status Updated Successfully.',
        'select_one_user' => 'Please select at least 1 user.',
        'email_must_be_unique' => 'The Email has already been taken.',
        'password_required' => 'The password field is required.',
        'fmobile_required' => 'Mobile field is required.',
        'fmobile_unique' => 'The Mobile has already been taken.',
        'password_must_be_valid' => 'Password must be at least 8 characters 1'.
        'uppercase 1 lowercase 1 special character and 1 number.',
    ],
    'admins' => [
        'first_name' => FIRST_NAME,
        'last_name' => LAST_NAME,
        'email' => 'Email',
        'mobile' => 'Mobile',
        'created_at' => 'Created At',
        'role' => 'Role',
        'mobile_number' => MOBILE_NUMBER,
        'admin_user' => 'Admin User',
        'admins' => 'Admins',
        'add_success' => 'Admin has been added successfully.',
        'edit_success' => 'Admin has been updated successfully.',
        'edit_password' => 'Admin has been updated password successfully.',
        'delete_success' => 'Admin has been deleted successfully.',
        'status_change' => 'Admin has been :status successfully.',
        'add_admin' => 'Add admin',
        'edit_admin' => 'Edit admin',
        'admin_deleted' => 'Admin Deleted!',
        'back_to_listing' => 'Back to the admins list',
        'select_one_admin' => 'Please select at least 1 admin.',
        'fmobile_required' => 'Mobile field is required.',
        'fmobile_unique' => 'The Mobile has already been taken.',
        'fmobile_may_not_contain_whitespace' => 'The mobile may not contain whitespace.',
        'status_updated' => 'Status Updated Successfully.',
        'password_must_be_valid' => 'Password must be at least 8 characters 1'.
        'uppercase 1 lowercase 1 special character and 1 number.',
    ],
    'modules' => [
        'name' => 'Name',
        'display_name' => DISPLAY_NAME,
        'description' => 'Description',
        'is_moduled' => 'Module Type',
        'modules' => 'Modules',
        'is_module' => 'Is Module:',
        'add_success' => 'Module has been added successfully.',
        'edit_success' => 'Module has been updated successfully.',
        'delete_success' => 'Module has been deleted successfully.',
        'status_change' => 'Module has been :status successfully.',
        'add_module' => 'Add Module',
        'edit_module' => 'Edit Module',
        'module_deleted' => 'Module Deleted!',
        'back_to_listing' => 'Back to the Modules list',
        'module_enabled_success' => 'Module Enabled successfully',
        'module_disabled_success' => 'Module Disabled successfully',
        'select_dependent_modules' => 'Select dependent modules',
        'dependent_modules' => 'Dependent modules',
        'actual_name' => 'Actual Name',
        'status_success' => 'Updated successfully !',
        'module_not_exist' => 'Module not exist with this name ! ',
        'module_not_found' => 'Module Not Found ! ',
        'module_exist' => 'Module exist with this name',
        'name_placeholder' => 'Enter name in smallcase and add hyphen if multiple words, ex:email-templates',
        'display_name_placeholder' => 'Enter display name',
        'description_placeholder' => 'Enter description',
        'moduled_based_enable' => 'Are you sure you want to enable as moduled-based module ?',
        'moduled_based_disable' => 'Are you sure you want to disable as moduled-based module ? ',
    ],
    'permissions' => [
        'name' => 'Name',
        'display_name' => DISPLAY_NAME,
        'description' => 'Description',
        'permissions' => 'Permissions',
        'modules' => 'Modules',
        'add_success' => 'Permission has been added successfully.',
        'edit_success' => 'Permission has been updated successfully.',
        'delete_success' => 'Permission has been deleted successfully.',
        'status_change' => 'Permission has been :status successfully.',
        'add_permission' => 'Add Permission',
        'edit_permission' => 'Edit Permission',
        'permission_deleted' => 'Permission Deleted!',
        'back_to_listing' => 'Back to the Permissions list',
    ],
    'roles' => [
        'name' => 'Name',
        'display_name' => DISPLAY_NAME,
        'description' => 'Description',
        'roles' => 'Roles',
        'role' => 'Role',
        'add_success' => 'Role has been added successfully.',
        'edit_success' => 'Role has been updated successfully.',
        'delete_success' => 'Role has been deleted successfully.',
        'status_change' => 'Role has been :status successfully.',
        'add_role' => 'Add Role',
        'edit_role' => 'Edit Role',
        'role_deleted' => 'Role Deleted!',
        'back_to_listing' => 'Back to the Roles list',
        'name_may_not_contain_whitespace' => 'The name may not contain whitespace.',
        'display_may_not_contain_whitespace' => 'The display name may not contain whitespace.',
        'permissions' => 'Permissions'
    ],
    'sitemap' => [
        'generate_success' => 'Your Sitemap xml file is generated, download it from Sitemap
        section and Upload it into the domain root folder of your website.',
        'exist_error' => 'Sitemap file does not exist, kindly create one first then download.',
    ],
    'error_logs' => [
        'error_logs' => 'Error Logs',
        'message' => 'Message',
        'file' => 'File',
        'line' => 'Line',
        'class' => 'Class',
        'route_name' => 'Route Name',
        'action' => 'Action',
        'middlewares' => 'Middlewares',
        'method' => 'Method',
        'url' => 'Url',
        'ip' => 'Ip',
        'inputs' => 'Inputs',
        'browser' => 'Browser',
        'php_version' => 'Php Version',
        'laravel_version' => 'Laravel Version',
        'app_debug' => 'App Debug',
        'app_env' => 'App Env',
        'database' => 'Database',
        'mysql_version' => 'Mysql Version',
        'locale' => 'Locale',
        'timezone' => 'Timezone',
        'headers' => 'Headers',
        'trace_arr' => 'Trace Array',
        'show' => 'Show',
    ],
    'dynamic_import' => [
        'breadcrumb' => [
            'index' => DYNAMIC_IMPORT,
            'add' => 'Add Dynamic Import',
            'edit' => 'Edit Dynamic Import',
            'view' => 'View Dynamic Import',
        ],
        'title' => [
            'index' => DYNAMIC_IMPORT,
            'add' => 'Add Dynamic Import',
            'edit' => 'Edit Dynamic Import',
            'view' => 'View Dynamic Import',
        ],
        'label' => [
            'table_name' => 'Table Name',
            'table_field' => 'Table Field',
            'excel_header' => 'Excel Header',
        ],
        'successResponse' => [
            'template_added' => ':successCount rows imported successfully. :failCount rows failed.',
            'template_deleted' => 'File Successfully Deleted.',
        ],
        'errorResponse' => [
            'something_wrong' => 'Something went wrong.',
            'field_not_found' => 'Field Detail not found for the Selected Template.',
            'csv_import' => 'Error importing CSV file. Please check the logs for details',
        ],
        'table' => [
            'template_name' => 'Template name',
            'action' => 'Action',
            'table_name_required' => 'The Table Name field is required.',
        ],
        'form' => [
            'template_name' => 'Template name',
            'table_name' => 'Table Name',
            'file' => 'Choose File',
            'select' => 'Select',
            'field_name' => 'Field Name',
        ],
        'buttons' => [
            'import' => 'Import',
            'cancel' => 'Cancel',
        ],
    ],
    'global_setting' => [
        'company' => 'Company',
        'smtp' => 'SMTP',
        'social_media' => 'Social Media',
        'company_name' => 'Company Name',
        'email' => 'Email',
        'address' => 'Address',
        'toll_free' => 'Phone Toll Free',
        'phone' => 'Phone',
        'fax' => 'Fax',
        'copyright' => 'Copyright',
        'facebook_url' => 'Facebook URL',
        'linkedin_url' => 'Linkedin URL',
        'google' => 'Google',
        'twitter' => 'Twitter',
        'instagram' => 'Instagram',
        'pintrest' => 'Pintrest',
        'smtp_host' => 'SMTP Host',
        'smtp_driver' => 'SMTP Driver',
        'smtp_port' => 'SMTP Port',
        'smtp_username' => 'SMTP Username',
        'smtp_password' => 'SMTP Password',
        'smtp_address' => 'SMTP From Email Address',
        'smtp_name' => 'SMTP From Name',
        'facebook' => 'Facebook',
        'facebook_app' => 'Facebook App Id',
        'fsk' => 'Facebook Secret Key',
        'twitter_key' => 'Twitter Key',
        'tsk' => 'Twitter Secret Key',
        'website_url' => 'Website Url',
        'gen_sitemap' => 'Genterate Sitemap xml',
        'confirmation_status' => 'Are you sure you want to :status this module ?',
        'dependant_status' => ' dependent on this module, continue to :status ?',
        'success_msg' => 'Data Store Successfull',
        'maintenance' => 'Setting has been updated',
        'updated' => 'Setting has been updated',
        'company_name_may_not_contain_whitespace' => 'The company name may not contain whitespace.',
        'company_address_may_not_contain_whitespace' => 'The address may not contain whitespace.',
        'company_email_may_not_contain_whitespace' => 'The email may not contain whitespace.',
        'company_phone_toll_free_may_not_contain_whitespace' => 'The phone toll free may not contain whitespace.',
        'company_phone_may_not_contain_whitespace' => 'The phone may not contain whitespace.',
        'company_phone_required' => 'The phone field is required.',
        'company_fax_may_not_contain_whitespace' => 'The fax may not contain whitespace.',
        'company_copyright_text_may_not_contain_whitespace' => 'The copyright may not contain whitespace.',
        'company_facebook_url_may_not_contain_whitespace' => 'The facebook url may not contain whitespace.',
        'company_linkedin_url_may_not_contain_whitespace' => 'The linkedin url may not contain whitespace.',
        'company_google_url_may_not_contain_whitespace' => 'The google url may not contain whitespace.',
        'company_twitter_url_may_not_contain_whitespace' => 'The twitter url may not contain whitespace.',
        'company_instagram_url_may_not_contain_whitespace' => 'The instagram url may not contain whitespace.',
        'company_pintrest_url_may_not_contain_whitespace' => 'The pintrest url may not contain whitespace.',
        'facebook_secret_key_may_not_contain_whitespace' => 'The facebook secret key may not contain whitespace.',
        'twitter_key_may_not_contain_whitespace' => 'The twitter key may not contain whitespace.',
        'twitter_secret_key_may_not_contain_whitespace' => 'The twitter secret key may not contain whitespace.',
        'facebook_app_id_may_not_contain_whitespace' => 'The facebook app id may not contain whitespace.',
        'mail_smtp_host_may_not_contain_whitespace' => 'The smatp host may not contain whitespace.',
        'mail_smtp_driver_may_not_contain_whitespace' => 'The smtp driver may not contain whitespace.',
        'mail_smtp_mail_from_name_may_not_contain_whitespace' => 'The smtp from name may not contain whitespace.',
        'website_url_may_not_contain_whitespace' => 'The website url may not contain whitespace.',
        'grid_column_search' => 'All grid Column search manage',
        'date_formate' => 'Set Admin date format',
        'time_format' => 'Set Admin time format',
        'deci_nu_format' => 'Set Global decimal number',
        'deci_point_format' => 'decimal point',
        'deci_sep_format' => 'Decimal Separator',
        'smtp_encryption' => 'SMTP Encryption',
        'site_maintenance_enable' => 'You want to initiate maintenance for the website?' .
            'This action will temporarily make the site unavailable to users!',
        'site_maintenance_disable' => 'You want to disable maintenance for the website?' .
            'This action will make the site available to users!',
        'site_maintenance'  => 'Site Under Maintenance'
    ],
    'dashboard' => [
        'no_records_found' => 'No Records Found',
        'display_name' => 'Display Name',
        'description' => 'Description',
        'name' => 'Name',
        'email' => 'Email',
        'status' => 'Status',
        'total_modules' => 'Total Modules',
        'module' => 'Module',
        'total_roles' => 'Total Roles',
        'role' => 'Role',
        'total_admins' => 'Total Admins',
        'admin' => 'Admin',
        'total_users' => 'Total Users',
        'user' => 'User'
    ],
    'footer' => [
        'profile_picture' => 'profile picture.',
        'update_profile' => 'Update Profile'
    ],
    'date_format' => [
        "d-m-Y" =>"d-m-Y",
        "d/m/Y" =>"d/m/Y",
        "d.m.Y" =>"d.m.Y",
        "m-d-Y" =>"m-d-Y",
        "m/d/Y" =>"m/d/Y",
        "m.d.Y" =>"m.d.Y",
        "Y-m-d" =>"Y-m-d",
        "Y/m/d" =>"Y/m/d",
        "Y.m.d" =>"Y.m.d",
        "Y-d-m" =>"Y-d-m",
        "Y/d/m" =>"Y/d/m",
        "Y.d.m" =>"Y.d.m"
    ],

    'time_format' => [
        "h:i a"=>"h:i a",
        "h:i:s"=> "h:i:s",
        "h:i A" => "h:i A",
        "H:i" =>"H:i",
        'H:i a'=> 'H:i a'
    ],
    'deci_nu_format' => [
        "0"=>0,
        "1"=> 1,
        "2" => 2,
        "3" =>3,
        "4" =>4,
        "5" =>5,
    ],

    'deci_point_format' => [
        "."=>". (Full Stop)",
        ","=> ", (Comma)",
        "'" => ". (Apostrophe)",
        "none" =>"None",
        "space" =>"Space"
    ],
    'deci_sep_format' => [
        "."=>". (Full Stop)",
        ","=> ", (Comma)"
    ]
];
