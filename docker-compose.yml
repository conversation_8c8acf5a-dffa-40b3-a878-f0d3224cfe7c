services:
    laravel.test:
        build:
            context: ./vendor/laravel/sail/runtimes/8.2
            dockerfile: Dockerfile
            args:
                WWWGROUP: "${WWWGROUP}"
        image: sail-8.2/app
        extra_hosts:
            - "host.docker.internal:host-gateway"
        ports:
            - "${APP_PORT:-80}:80"
            - "${VITE_PORT:-5173}:${VITE_PORT:-5173}"
        environment:
            WWWUSER: "${WWWUSER}"
            LARAVEL_SAIL: 1
            XDEBUG_MODE: "${SAIL_XDEBUG_MODE:-off}"
            XDEBUG_CONFIG: "${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}"
            IGNITION_LOCAL_SITES_PATH: "${PWD}"
        volumes:
            - ".:/var/www/html"
        networks:
            - sail
        depends_on:
            - mysql
            - redis
            - meilisearch
            - mailpit
    mysql:
        image: "mysql/mysql-server:8.0"
        ports:
            - "${FORWARD_DB_PORT:-3306}:3306"
        environment:
            MYSQL_ROOT_PASSWORD: "${DB_PASSWORD}"
            MYSQL_ROOT_HOST: "%"
            MYSQL_DATABASE: "${DB_DATABASE}"
            MYSQL_USER: "${DB_USERNAME}"
            MYSQL_PASSWORD: "${DB_PASSWORD}"
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - "sail-mysql:/var/lib/mysql"
            - "./vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh"
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - mysqladmin
                - ping
                - "-p${DB_PASSWORD}"
            retries: 3
            timeout: 5s
    redis:
        image: "redis:alpine"
        ports:
            - "${FORWARD_REDIS_PORT:-6379}:6379"
        volumes:
            - "sail-redis:/data"
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    meilisearch:
        image: "getmeili/meilisearch:latest"
        ports:
            - "${FORWARD_MEILISEARCH_PORT:-7700}:7700"
        environment:
            MEILI_NO_ANALYTICS: "${MEILISEARCH_NO_ANALYTICS:-false}"
        volumes:
            - "sail-meilisearch:/meili_data"
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - wget
                - "--no-verbose"
                - "--spider"
                - "http://localhost:7700/health"
            retries: 3
            timeout: 5s
    mailpit:
        image: "axllent/mailpit:latest"
        ports:
            - "${FORWARD_MAILPIT_PORT:-1025}:1025"
            - "${FORWARD_MAILPIT_DASHBOARD_PORT:-8025}:8025"
        networks:
            - sail

    adminer:
        image: "adminer"
        ports:
            - "8081:8080"
        environment:
            ADMINER_DESIGN: "pepa-linha"
            ADMINER_DEFAULT_SERVER: "mysql"
        networks:
            - sail
        depends_on:
            - mysql
networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
    sail-redis:
        driver: local
    sail-meilisearch:
        driver: local
